// Shipping calculation utilities

/**
 * Shipping rates configuration by country and method
 */
export const SHIPPING_RATES = {
  cambodia: {
    standard: { base: 2.99, perKg: 1.50, days: '3-5', description: 'Standard delivery via local courier' },
    express: { base: 7.99, perKg: 3.00, days: '1-2', description: 'Express delivery within 1-2 business days' },
    overnight: { base: 15.99, perKg: 5.00, days: '1', description: 'Overnight delivery by next business day' }
  },
  'united-states': {
    standard: { base: 8.99, perKg: 4.50, days: '7-10', description: 'Standard international shipping' },
    express: { base: 19.99, perKg: 8.00, days: '3-5', description: 'Express international shipping' },
    overnight: { base: 39.99, perKg: 12.00, days: '1-2', description: 'Priority international express' }
  },
  canada: {
    standard: { base: 9.99, perKg: 5.00, days: '7-12', description: 'Standard international shipping' },
    express: { base: 22.99, perKg: 9.00, days: '4-6', description: 'Express international shipping' },
    overnight: { base: 42.99, perKg: 14.00, days: '1-3', description: 'Priority international express' }
  },
  japan: {
    standard: { base: 12.99, perKg: 6.50, days: '10-14', description: 'Standard international shipping' },
    express: { base: 28.99, perKg: 12.00, days: '5-7', description: 'Express international shipping' },
    overnight: { base: 49.99, perKg: 18.00, days: '2-3', description: 'Priority international express' }
  }
};

/**
 * Country and state/province data
 */
export const SHIPPING_LOCATIONS = {
  cambodia: {
    name: 'Cambodia',
    states: [
      { value: 'phnom-penh', name: 'Phnom Penh' },
      { value: 'siem-reap', name: 'Siem Reap' },
      { value: 'battambang', name: 'Battambang' },
      { value: 'kampong-cham', name: 'Kampong Cham' },
      { value: 'kampong-speu', name: 'Kampong Speu' },
      { value: 'kandal', name: 'Kandal' }
    ]
  },
  'united-states': {
    name: 'United States',
    states: [
      { value: 'california', name: 'California' },
      { value: 'new-york', name: 'New York' },
      { value: 'texas', name: 'Texas' },
      { value: 'florida', name: 'Florida' },
      { value: 'illinois', name: 'Illinois' },
      { value: 'pennsylvania', name: 'Pennsylvania' }
    ]
  },
  canada: {
    name: 'Canada',
    states: [
      { value: 'ontario', name: 'Ontario' },
      { value: 'quebec', name: 'Quebec' },
      { value: 'british-columbia', name: 'British Columbia' },
      { value: 'alberta', name: 'Alberta' },
      { value: 'manitoba', name: 'Manitoba' },
      { value: 'saskatchewan', name: 'Saskatchewan' }
    ]
  },
  japan: {
    name: 'Japan',
    states: [
      { value: 'tokyo', name: 'Tokyo' },
      { value: 'osaka', name: 'Osaka' },
      { value: 'kyoto', name: 'Kyoto' },
      { value: 'yokohama', name: 'Yokohama' },
      { value: 'nagoya', name: 'Nagoya' },
      { value: 'sapporo', name: 'Sapporo' }
    ]
  }
};

/**
 * Calculate package weight based on cart items
 * @param {Array} cartItems - Array of cart items
 * @param {number} bookWeight - Weight per book in kg (default: 0.5)
 * @returns {number} Total weight in kg
 */
export const calculatePackageWeight = (cartItems, bookWeight = 0.5) => {
  return cartItems.reduce((total, item) => {
    const quantity = parseInt(item.quantity) || 1;
    return total + (quantity * bookWeight);
  }, 0);
};

/**
 * Calculate shipping cost based on country, method, and weight
 * @param {string} country - Country code
 * @param {string} method - Shipping method (standard, express, overnight)
 * @param {number} weight - Package weight in kg
 * @returns {Object} Shipping calculation result
 */
export const calculateShippingCost = (country, method, weight) => {
  const countryRates = SHIPPING_RATES[country] || SHIPPING_RATES.cambodia;
  const methodRate = countryRates[method] || countryRates.standard;
  
  // Calculate cost: base rate + additional weight charges
  let cost = methodRate.base;
  if (weight > 1) {
    cost += (weight - 1) * methodRate.perKg;
  }
  
  return {
    cost: Math.max(cost, 0),
    estimatedDays: methodRate.days,
    description: methodRate.description,
    method: method,
    country: country
  };
};

/**
 * Check if order qualifies for free shipping
 * @param {number} subtotal - Cart subtotal
 * @param {number} freeShippingThreshold - Minimum amount for free shipping
 * @returns {boolean} Whether order qualifies for free shipping
 */
export const qualifiesForFreeShipping = (subtotal, freeShippingThreshold = 50) => {
  return subtotal >= freeShippingThreshold;
};

/**
 * Get final shipping cost considering free shipping promotions
 * @param {number} subtotal - Cart subtotal
 * @param {number} calculatedShippingCost - Calculated shipping cost
 * @param {number} freeShippingThreshold - Minimum amount for free shipping
 * @returns {number} Final shipping cost
 */
export const getFinalShippingCost = (subtotal, calculatedShippingCost, freeShippingThreshold = 50) => {
  return qualifiesForFreeShipping(subtotal, freeShippingThreshold) ? 0 : calculatedShippingCost;
};

/**
 * Get shipping methods for a specific country
 * @param {string} country - Country code
 * @returns {Array} Available shipping methods
 */
export const getShippingMethods = (country) => {
  const countryRates = SHIPPING_RATES[country] || SHIPPING_RATES.cambodia;
  
  return Object.keys(countryRates).map(method => ({
    value: method,
    name: method.charAt(0).toUpperCase() + method.slice(1) + ' Shipping',
    ...countryRates[method]
  }));
};

/**
 * Validate shipping information
 * @param {Object} shippingInfo - Shipping information object
 * @returns {Object} Validation result
 */
export const validateShippingInfo = (shippingInfo) => {
  const errors = [];
  
  if (!shippingInfo.country) {
    errors.push('Country is required');
  }
  
  if (!shippingInfo.state) {
    errors.push('State/Province is required');
  }
  
  if (!shippingInfo.postalCode || shippingInfo.postalCode.trim().length < 3) {
    errors.push('Valid postal code is required');
  }
  
  if (!shippingInfo.shippingMethod) {
    errors.push('Shipping method is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Format shipping information for display
 * @param {Object} shippingInfo - Shipping information object
 * @returns {string} Formatted shipping address
 */
export const formatShippingAddress = (shippingInfo) => {
  const country = SHIPPING_LOCATIONS[shippingInfo.country];
  const state = country?.states.find(s => s.value === shippingInfo.state);
  
  const parts = [
    shippingInfo.address,
    state?.name,
    shippingInfo.postalCode,
    country?.name
  ].filter(Boolean);
  
  return parts.join(', ');
};

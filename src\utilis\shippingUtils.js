// Shipping calculation utilities

/**
 * Shipping rates configuration - Cambodia only
 */
export const SHIPPING_RATES = {
  cambodia: {
    standard: { base: 2.99, perKg: 1.50, days: '3-5', description: 'Standard delivery via local courier' },
    express: { base: 7.99, perKg: 3.00, days: '1-2', description: 'Express delivery within 1-2 business days' },
    overnight: { base: 15.99, perKg: 5.00, days: '1', description: 'Overnight delivery by next business day' }
  }
};

/**
 * Cambodia provinces/cities for delivery
 */
export const SHIPPING_LOCATIONS = {
  cambodia: {
    name: 'Cambodia',
    states: [
      { value: 'phnom-penh', name: 'Phnom Penh' },
      { value: 'siem-reap', name: '<PERSON><PERSON> Reap' },
      { value: 'battambang', name: 'Battambang' },
      { value: 'kampong-cham', name: 'Kampong Cham' },
      { value: 'kampong-speu', name: 'Kampong Speu' },
      { value: 'kandal', name: 'Kandal' },
      { value: 'kampot', name: 'Kampo<PERSON>' },
      { value: 'sihanoukville', name: 'Sihanoukville' },
      { value: 'takeo', name: '<PERSON><PERSON>' },
      { value: 'kampong-thom', name: 'Kampong Thom' },
      { value: 'prey-veng', name: 'Prey Veng' },
      { value: 'svay-rieng', name: 'Svay Rieng' }
    ]
  }
};

/**
 * Calculate package weight based on cart items
 * @param {Array} cartItems - Array of cart items
 * @param {number} bookWeight - Weight per book in kg (default: 0.5)
 * @returns {number} Total weight in kg
 */
export const calculatePackageWeight = (cartItems, bookWeight = 0.5) => {
  return cartItems.reduce((total, item) => {
    const quantity = parseInt(item.quantity) || 1;
    return total + (quantity * bookWeight);
  }, 0);
};

/**
 * Calculate shipping cost based on country, method, and weight
 * @param {string} country - Country code
 * @param {string} method - Shipping method (standard, express, overnight)
 * @param {number} weight - Package weight in kg
 * @returns {Object} Shipping calculation result
 */
export const calculateShippingCost = (country, method, weight) => {
  const countryRates = SHIPPING_RATES[country] || SHIPPING_RATES.cambodia;
  const methodRate = countryRates[method] || countryRates.standard;
  
  // Calculate cost: base rate + additional weight charges
  let cost = methodRate.base;
  if (weight > 1) {
    cost += (weight - 1) * methodRate.perKg;
  }
  
  return {
    cost: Math.max(cost, 0),
    estimatedDays: methodRate.days,
    description: methodRate.description,
    method: method,
    country: country
  };
};

/**
 * Check if order qualifies for free shipping
 * @param {number} subtotal - Cart subtotal
 * @param {number} freeShippingThreshold - Minimum amount for free shipping
 * @returns {boolean} Whether order qualifies for free shipping
 */
export const qualifiesForFreeShipping = (subtotal, freeShippingThreshold = 50) => {
  return subtotal >= freeShippingThreshold;
};

/**
 * Get final shipping cost considering free shipping promotions
 * @param {number} subtotal - Cart subtotal
 * @param {number} calculatedShippingCost - Calculated shipping cost
 * @param {number} freeShippingThreshold - Minimum amount for free shipping
 * @returns {number} Final shipping cost
 */
export const getFinalShippingCost = (subtotal, calculatedShippingCost, freeShippingThreshold = 50) => {
  return qualifiesForFreeShipping(subtotal, freeShippingThreshold) ? 0 : calculatedShippingCost;
};

/**
 * Get delivery methods for Cambodia
 * @param {string} country - Country code (defaults to cambodia)
 * @returns {Array} Available delivery methods
 */
export const getShippingMethods = (country = 'cambodia') => {
  const countryRates = SHIPPING_RATES.cambodia;

  return Object.keys(countryRates).map(method => ({
    value: method,
    name: method.charAt(0).toUpperCase() + method.slice(1) + ' Delivery',
    ...countryRates[method]
  }));
};

/**
 * Validate delivery information for Cambodia
 * @param {Object} shippingInfo - Shipping information object
 * @returns {Object} Validation result
 */
export const validateShippingInfo = (shippingInfo) => {
  const errors = [];

  if (!shippingInfo.state) {
    errors.push('Province/City is required');
  }

  if (!shippingInfo.shippingMethod) {
    errors.push('Delivery method is required');
  }

  if (!shippingInfo.address || shippingInfo.address.trim().length < 10) {
    errors.push('Complete delivery address is required (minimum 10 characters)');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Format shipping information for display
 * @param {Object} shippingInfo - Shipping information object
 * @returns {string} Formatted shipping address
 */
export const formatShippingAddress = (shippingInfo) => {
  const country = SHIPPING_LOCATIONS[shippingInfo.country];
  const state = country?.states.find(s => s.value === shippingInfo.state);
  
  const parts = [
    shippingInfo.address,
    state?.name,
    shippingInfo.postalCode,
    country?.name
  ].filter(Boolean);
  
  return parts.join(', ');
};

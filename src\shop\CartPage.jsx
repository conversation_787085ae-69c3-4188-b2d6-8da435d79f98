import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import PageHeader from "../components/PageHeader";
import delImgUrl from "../assets/images/shop/del.png";
import CheckoutPage from "./CheckOutPage";
import { getImageUrl } from "../utilis/apiService";
import {
  calculatePackageWeight,
  calculateShippingCost,
  validateShippingInfo
} from "../utilis/shippingUtils";

// Fallback image in case the item image is missing or broken
const fallbackImage = "/assets/images/product-placeholder.png";

const CartPage = () => {
  const [cartItems, setCartItems] = useState([]);
  const [error, setError] = useState(null);

  // Delivery information state (simplified)
  const [shippingInfo, setShippingInfo] = useState({
    phone: '',
    address: ''
  });
  const [shippingCost, setShippingCost] = useState(0);
  const [estimatedDelivery, setEstimatedDelivery] = useState('');
  const [shippingCalculated, setShippingCalculated] = useState(false);

  useEffect(() => {
    try {
      // fetch cart items from local storage
      const storedCartItems = JSON.parse(localStorage.getItem("cart")) || [];

      // Parse prices to ensure they're numbers
      const parsedCartItems = storedCartItems.map((item) => ({
        ...item,
        price: parseFloat(item.price) || 0,
        quantity: parseInt(item.quantity) || 1,
      }));

      setCartItems(parsedCartItems);
    } catch (error) {
      console.error("Error loading cart:", error);
      setError("Failed to load cart items");
    }
  }, []);

  // calculate prices
  const calculateTotalPrice = (item) => {
    const price = parseFloat(item.price) || 0;
    const quantity = parseInt(item.quantity) || 1;
    return price * quantity;
  };

  // handle quantity increase
  const handleIncrease = (item) => {
    try {
      item.quantity = parseInt(item.quantity) + 1;
      setCartItems([...cartItems]);

      // update local storage with new cart items
      updateLocalStorage(cartItems);
    } catch (error) {
      console.error("Error increasing quantity:", error);
    }
  };

  // handle quantity decrease
  const handleDecrease = (item) => {
    try {
      if (parseInt(item.quantity) > 1) {
        item.quantity = parseInt(item.quantity) - 1;
        setCartItems([...cartItems]);

        // update local storage with new cart items
        updateLocalStorage(cartItems);
      }
    } catch (error) {
      console.error("Error decreasing quantity:", error);
    }
  };

  // handle item removal
  const handleRemoveItem = (item) => {
    try {
      const updatedCart = cartItems.filter(
        (cartItem) => cartItem.id !== item.id
      );

      // update new cart
      setCartItems(updatedCart);

      updateLocalStorage(updatedCart);
    } catch (error) {
      console.error("Error removing item:", error);
    }
  };

  const updateLocalStorage = (cart) => {
    try {
      localStorage.setItem("cart", JSON.stringify(cart));
    } catch (error) {
      console.error("Error updating cart in localStorage:", error);
      setError("Failed to update cart");
    }
  };

  // Shipping calculation functions
  const calculateWeight = () => {
    return calculatePackageWeight(cartItems);
  };

  const handleShippingInfoChange = (field, value) => {
    setShippingInfo(prev => ({
      ...prev,
      [field]: value
    }));

    // Reset calculation when shipping info changes
    if (shippingCalculated) {
      setShippingCalculated(false);
    }
  };

  const calculateShipping = () => {
    // Validate delivery information
    const validation = validateShippingInfo(shippingInfo);
    if (!validation.isValid) {
      alert('Please fill in delivery information:\n' + validation.errors.join('\n'));
      return;
    }

    // Fixed $1 delivery cost for Cambodia
    const shipping = calculateShippingCost();
    setShippingCost(shipping.cost);
    setEstimatedDelivery(shipping.estimatedDays);
    setShippingCalculated(true);
  };

  // cart subtotal
  const cartSubTotal = cartItems.reduce((total, item) => {
    return total + calculateTotalPrice(item);
  }, 0);

  // order total
  const orderTotal = cartSubTotal + shippingCost;

  if (error) {
    return (
      <div className="container py-5 text-center">
        <h3 className="text-danger">{error}</h3>
        <Link to="/shop" className="btn btn-primary mt-3">
          Return to Shop
        </Link>
      </div>
    );
  }

  return (
    <div>
      <PageHeader title={"Shop Cart"} curPage={"Cart Page"} />

      <div className="shop-cart padding-tb">
        <div className="container">
          <div className="section-wrapper">
            {cartItems.length === 0 ? (
              <div className="text-center py-5">
                <h4>Your cart is empty</h4>
                <Link to="/shop" className="btn btn-primary mt-3">
                  Browse Books
                </Link>
              </div>
            ) : (
              <>
                {/* cart top */}
                <div className="cart-top">
                  <table>
                    <thead>
                      <tr>
                        <th className="cat-product">Product</th>
                        <th className="cat-price">Price</th>
                        <th className="cat-quantity">Quantity</th>
                        <th className="cat-toprice">Total</th>
                        <th className="cat-edit">Edit</th>
                      </tr>
                    </thead>

                    {/* table body */}
                    <tbody>
                      {cartItems.map((item, indx) => (
                        <tr key={indx}>
                          <td className="product-item cat-product">
                            <div className="p-thumb">
                              <Link to={`/shop/${item.id}`}>
                                {/* Use BookID to display the image */}
                                <img
                                  src={
                                    item.BookID
                                      ? getImageUrl(
                                          `books/${item.BookID}/image`
                                        )
                                      : fallbackImage
                                  }
                                  alt={item.name}
                                  onError={(e) => {
                                    e.target.src = fallbackImage;
                                  }}
                                />
                              </Link>
                            </div>
                            <div className="p-content">
                              <Link to={`/shop/${item.id}`}>{item.name}</Link>
                            </div>
                          </td>

                          <td className="cat-price">
                            ${parseFloat(item.price).toFixed(2)}
                          </td>

                          <td className="cat-quantity">
                            <div className="cart-plus-minus">
                              <div
                                className="dec qtybutton"
                                onClick={() => handleDecrease(item)}
                              >
                                -
                              </div>
                              <input
                                type="text"
                                className="cart-plus-minus-box"
                                name="qtybutton"
                                value={item.quantity}
                                readOnly
                              />
                              <div
                                className="inc qtybutton"
                                onClick={() => handleIncrease(item)}
                              >
                                +
                              </div>
                            </div>
                          </td>

                          <td className="cat-toprice">
                            ${calculateTotalPrice(item).toFixed(2)}
                          </td>

                          <td className="cat-edit">
                            <button
                              type="button"
                              onClick={() => handleRemoveItem(item)}
                              style={{
                                background: 'none',
                                border: 'none',
                                padding: '0',
                                cursor: 'pointer'
                              }}
                              title="Remove item from cart"
                            >
                              <img src={delImgUrl} alt="Delete Item" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* cart top ends */}

                {/* card bottom */}
                <div className="cart-bottom">
                  {/* checkout box */}
                  <div className="cart-checkout-box">
                    <form
                      className="coupon"
                      onSubmit={(e) => e.preventDefault()}
                    >
                      <input
                        className="cart-page-input-text"
                        type="text"
                        name="coupon"
                        id="coupon"
                        placeholder="Coupon code ...."
                      />
                      <input type="submit" value={"Apply Coupon"} />
                    </form>
                    <form
                      className="cart-checkout"
                      onSubmit={(e) => e.preventDefault()}
                    >
                      <input type="submit" value="Update Cart" />
                      <div>
                        {/* Pass the orderTotal, cartItems, and shippingCost to CheckoutPage */}
                        <CheckoutPage
                          orderTotal={orderTotal}
                          cartItems={cartItems}
                          shippingCost={shippingCost}
                        />
                      </div>
                    </form>
                  </div>

                  {/* checkout box end */}

                  {/* shopping box */}
                  <div className="shiping-box">
                    <div className="row">
                      <div className="col-md-6 col-12">
                        <div className="calculate-shiping">
                          <h3>🚚 Delivery Information</h3>
                          <div className="delivery-notice mb-3 p-3" style={{backgroundColor: '#f8f9fa', border: '1px solid #dee2e6', borderRadius: '8px'}}>
                            <div className="d-flex align-items-center">
                              <span style={{fontSize: '1.2em', marginRight: '8px'}}>🇰🇭</span>
                              <strong>Cambodia Delivery - $1.00</strong>
                            </div>
                            <small className="text-muted">Fixed delivery cost within Cambodia</small>
                          </div>

                          {/* Phone Number */}
                          <div className="form-group mb-3">
                            <label className="form-label">Phone Number *</label>
                            <input
                              type="tel"
                              name="phone"
                              id="phone"
                              placeholder="Enter your phone number"
                              className="cart-page-input-text"
                              value={shippingInfo.phone}
                              onChange={(e) => handleShippingInfoChange('phone', e.target.value)}
                              required
                            />
                          </div>

                          {/* Address */}
                          <div className="form-group mb-3">
                            <label className="form-label">Delivery Address *</label>
                            <textarea
                              className="cart-page-input-text"
                              placeholder="Enter your complete delivery address..."
                              rows="3"
                              value={shippingInfo.address}
                              onChange={(e) => handleShippingInfoChange('address', e.target.value)}
                              required
                            ></textarea>
                          </div>

                          {/* Package Information */}
                          <div className="shipping-info mb-3 p-3" style={{backgroundColor: '#fff3cd', border: '1px solid #ffeaa7', borderRadius: '8px'}}>
                            <h6 className="d-flex align-items-center mb-2">
                              📦 Package Details
                            </h6>
                            <div className="row">
                              <div className="col-6">
                                <div className="info-item">
                                  <small className="text-muted">Total Items</small>
                                  <div className="fw-bold">{cartItems.reduce((total, item) => total + item.quantity, 0)} books</div>
                                </div>
                              </div>
                              <div className="col-6">
                                <div className="info-item">
                                  <small className="text-muted">Est. Weight</small>
                                  <div className="fw-bold">{calculateWeight().toFixed(1)} kg</div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <button
                            type="button"
                            className="btn btn-primary w-100"
                            onClick={calculateShipping}
                            disabled={!shippingInfo.phone || !shippingInfo.address.trim()}
                          >
                            Calculate Delivery ($1.00)
                          </button>

                          {/* Delivery Results */}
                          {shippingCalculated && (
                            <div className="shipping-results mt-3 p-3" style={{backgroundColor: '#d4edda', border: '1px solid #c3e6cb', borderRadius: '8px'}}>
                              <h6 className="d-flex align-items-center text-success mb-2">
                                ✅ Delivery Calculated
                              </h6>
                              <div className="row">
                                <div className="col-6">
                                  <small className="text-muted">Delivery Cost</small>
                                  <div className="fw-bold fs-5 text-success">
                                    $1.00
                                  </div>
                                </div>
                                <div className="col-6">
                                  <small className="text-muted">Estimated Time</small>
                                  <div className="fw-bold">2-3 business days</div>
                                </div>
                              </div>

                            </div>
                          )}
                        </div>
                      </div>
                      <div className="col-md-6 col-12">
                        <div className="cart-overview">
                          <h3>Cart Totals</h3>
                          <ul className="lab-ul">
                            <li>
                              <span className="pull-left">Cart Subtotal</span>
                              <p className="pull-right">
                                $ {cartSubTotal.toFixed(2)}
                              </p>
                            </li>
                            <li>
                              <span className="pull-left">Items Count</span>
                              <p className="pull-right">
                                {cartItems.reduce((total, item) => total + item.quantity, 0)} books
                              </p>
                            </li>
                            <li>
                              <span className="pull-left">Package Weight</span>
                              <p className="pull-right">
                                {calculateWeight().toFixed(1)} kg
                              </p>
                            </li>
                            <li>
                              <span className="pull-left">
                                Delivery and Handling
                              </span>
                              <p className="pull-right">
                                {!shippingCalculated ? (
                                  <span className="text-muted">Calculate delivery</span>
                                ) : (
                                  `$${shippingCost.toFixed(2)}`
                                )}
                              </p>
                            </li>
                            {shippingCalculated && estimatedDelivery && (
                              <li>
                                <span className="pull-left">Estimated Delivery</span>
                                <p className="pull-right">
                                  2-3 days
                                </p>
                              </li>
                            )}

                            <li className="order-total-row">
                              <span className="pull-left fw-bold">Order Total</span>
                              <p className="pull-right fw-bold fs-5">
                                $ {orderTotal.toFixed(2)}
                              </p>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPage;

import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import PageHeader from "../components/PageHeader";
import delImgUrl from "../assets/images/shop/del.png";
import CheckoutPage from "./CheckOutPage";
import { getImageUrl } from "../utilis/apiService";
import {
  calculatePackageWeight,
  calculateShippingCost,
  getFinalShippingCost,
  SHIPPING_LOCATIONS,
  getShippingMethods,
  validateShippingInfo
} from "../utilis/shippingUtils";

// Fallback image in case the item image is missing or broken
const fallbackImage = "/assets/images/product-placeholder.png";

const CartPage = () => {
  const [cartItems, setCartItems] = useState([]);
  const [error, setError] = useState(null);

  // Shipping calculation state
  const [shippingInfo, setShippingInfo] = useState({
    country: 'cambodia',
    state: 'phnom-penh',
    postalCode: '',
    shippingMethod: 'standard',
    address: ''
  });
  const [shippingCost, setShippingCost] = useState(0);
  const [estimatedDelivery, setEstimatedDelivery] = useState('');
  const [shippingCalculated, setShippingCalculated] = useState(false);

  useEffect(() => {
    try {
      // fetch cart items from local storage
      const storedCartItems = JSON.parse(localStorage.getItem("cart")) || [];

      // Parse prices to ensure they're numbers
      const parsedCartItems = storedCartItems.map((item) => ({
        ...item,
        price: parseFloat(item.price) || 0,
        quantity: parseInt(item.quantity) || 1,
      }));

      setCartItems(parsedCartItems);
    } catch (error) {
      console.error("Error loading cart:", error);
      setError("Failed to load cart items");
    }
  }, []);

  // calculate prices
  const calculateTotalPrice = (item) => {
    const price = parseFloat(item.price) || 0;
    const quantity = parseInt(item.quantity) || 1;
    return price * quantity;
  };

  // handle quantity increase
  const handleIncrease = (item) => {
    try {
      item.quantity = parseInt(item.quantity) + 1;
      setCartItems([...cartItems]);

      // update local storage with new cart items
      updateLocalStorage(cartItems);
    } catch (error) {
      console.error("Error increasing quantity:", error);
    }
  };

  // handle quantity decrease
  const handleDecrease = (item) => {
    try {
      if (parseInt(item.quantity) > 1) {
        item.quantity = parseInt(item.quantity) - 1;
        setCartItems([...cartItems]);

        // update local storage with new cart items
        updateLocalStorage(cartItems);
      }
    } catch (error) {
      console.error("Error decreasing quantity:", error);
    }
  };

  // handle item removal
  const handleRemoveItem = (item) => {
    try {
      const updatedCart = cartItems.filter(
        (cartItem) => cartItem.id !== item.id
      );

      // update new cart
      setCartItems(updatedCart);

      updateLocalStorage(updatedCart);
    } catch (error) {
      console.error("Error removing item:", error);
    }
  };

  const updateLocalStorage = (cart) => {
    try {
      localStorage.setItem("cart", JSON.stringify(cart));
    } catch (error) {
      console.error("Error updating cart in localStorage:", error);
      setError("Failed to update cart");
    }
  };

  // Shipping calculation functions
  const calculateWeight = () => {
    return calculatePackageWeight(cartItems);
  };

  const handleShippingInfoChange = (field, value) => {
    setShippingInfo(prev => ({
      ...prev,
      [field]: value
    }));

    // Reset calculation when shipping info changes
    if (shippingCalculated) {
      setShippingCalculated(false);
    }
  };

  const calculateShipping = () => {
    // Validate shipping information
    const validation = validateShippingInfo(shippingInfo);
    if (!validation.isValid) {
      alert('Please fill in all required shipping information:\n' + validation.errors.join('\n'));
      return;
    }

    const weight = calculateWeight();
    const shipping = calculateShippingCost(shippingInfo.country, shippingInfo.shippingMethod, weight);

    // Apply free shipping for orders over $50
    const finalCost = getFinalShippingCost(cartSubTotal, shipping.cost);

    setShippingCost(finalCost);
    setEstimatedDelivery(shipping.estimatedDays);
    setShippingCalculated(true);
  };

  // cart subtotal
  const cartSubTotal = cartItems.reduce((total, item) => {
    return total + calculateTotalPrice(item);
  }, 0);

  // order total
  const orderTotal = cartSubTotal + shippingCost;

  if (error) {
    return (
      <div className="container py-5 text-center">
        <h3 className="text-danger">{error}</h3>
        <Link to="/shop" className="btn btn-primary mt-3">
          Return to Shop
        </Link>
      </div>
    );
  }

  return (
    <div>
      <PageHeader title={"Shop Cart"} curPage={"Cart Page"} />

      <div className="shop-cart padding-tb">
        <div className="container">
          <div className="section-wrapper">
            {cartItems.length === 0 ? (
              <div className="text-center py-5">
                <h4>Your cart is empty</h4>
                <Link to="/shop" className="btn btn-primary mt-3">
                  Browse Books
                </Link>
              </div>
            ) : (
              <>
                {/* cart top */}
                <div className="cart-top">
                  <table>
                    <thead>
                      <tr>
                        <th className="cat-product">Product</th>
                        <th className="cat-price">Price</th>
                        <th className="cat-quantity">Quantity</th>
                        <th className="cat-toprice">Total</th>
                        <th className="cat-edit">Edit</th>
                      </tr>
                    </thead>

                    {/* table body */}
                    <tbody>
                      {cartItems.map((item, indx) => (
                        <tr key={indx}>
                          <td className="product-item cat-product">
                            <div className="p-thumb">
                              <Link to={`/shop/${item.id}`}>
                                {/* Use BookID to display the image */}
                                <img
                                  src={
                                    item.BookID
                                      ? getImageUrl(
                                          `books/${item.BookID}/image`
                                        )
                                      : fallbackImage
                                  }
                                  alt={item.name}
                                  onError={(e) => {
                                    e.target.src = fallbackImage;
                                  }}
                                />
                              </Link>
                            </div>
                            <div className="p-content">
                              <Link to={`/shop/${item.id}`}>{item.name}</Link>
                            </div>
                          </td>

                          <td className="cat-price">
                            ${parseFloat(item.price).toFixed(2)}
                          </td>

                          <td className="cat-quantity">
                            <div className="cart-plus-minus">
                              <div
                                className="dec qtybutton"
                                onClick={() => handleDecrease(item)}
                              >
                                -
                              </div>
                              <input
                                type="text"
                                className="cart-plus-minus-box"
                                name="qtybutton"
                                value={item.quantity}
                                readOnly
                              />
                              <div
                                className="inc qtybutton"
                                onClick={() => handleIncrease(item)}
                              >
                                +
                              </div>
                            </div>
                          </td>

                          <td className="cat-toprice">
                            ${calculateTotalPrice(item).toFixed(2)}
                          </td>

                          <td className="cat-edit">
                            <a
                              href="#"
                              onClick={(e) => {
                                e.preventDefault();
                                handleRemoveItem(item);
                              }}
                            >
                              <img src={delImgUrl} alt="Delete Item" />
                            </a>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* cart top ends */}

                {/* card bottom */}
                <div className="cart-bottom">
                  {/* checkout box */}
                  <div className="cart-checkout-box">
                    <form
                      className="coupon"
                      onSubmit={(e) => e.preventDefault()}
                    >
                      <input
                        className="cart-page-input-text"
                        type="text"
                        name="coupon"
                        id="coupon"
                        placeholder="Coupon code ...."
                      />
                      <input type="submit" value={"Apply Coupon"} />
                    </form>
                    <form
                      className="cart-checkout"
                      onSubmit={(e) => e.preventDefault()}
                    >
                      <input type="submit" value="Update Cart" />
                      <div>
                        {/* Pass the orderTotal, cartItems, and shippingCost to CheckoutPage */}
                        <CheckoutPage
                          orderTotal={orderTotal}
                          cartItems={cartItems}
                          shippingCost={shippingCost}
                        />
                      </div>
                    </form>
                  </div>

                  {/* checkout box end */}

                  {/* shopping box */}
                  <div className="shiping-box">
                    <div className="row">
                      <div className="col-md-6 col-12">
                        <div className="calculate-shiping">
                          <h3>Calculate Shipping</h3>

                          {/* Country Selection */}
                          <div className="form-group mb-3">
                            <label className="form-label">Country *</label>
                            <div className="outline-select">
                              <select
                                value={shippingInfo.country}
                                onChange={(e) => {
                                  handleShippingInfoChange('country', e.target.value);
                                  // Reset state when country changes
                                  const firstState = SHIPPING_LOCATIONS[e.target.value]?.states[0]?.value || '';
                                  handleShippingInfoChange('state', firstState);
                                }}
                              >
                                {Object.entries(SHIPPING_LOCATIONS).map(([code, location]) => (
                                  <option key={code} value={code}>
                                    {location.name}
                                  </option>
                                ))}
                              </select>
                              <span className="select-icon">
                                <i className="icofont-rounded-down"></i>
                              </span>
                            </div>
                          </div>

                          {/* State/Province Selection */}
                          <div className="form-group mb-3">
                            <label className="form-label">State/Province *</label>
                            <div className="outline-select shipping-select">
                              <select
                                value={shippingInfo.state}
                                onChange={(e) => handleShippingInfoChange('state', e.target.value)}
                              >
                                {SHIPPING_LOCATIONS[shippingInfo.country]?.states.map(state => (
                                  <option key={state.value} value={state.value}>
                                    {state.name}
                                  </option>
                                ))}
                              </select>
                              <span className="select-icon">
                                <i className="icofont-rounded-down"></i>
                              </span>
                            </div>
                          </div>

                          {/* Postal Code */}
                          <div className="form-group mb-3">
                            <label className="form-label">Postal Code/ZIP *</label>
                            <input
                              type="text"
                              name="postalCode"
                              id="postalCode"
                              placeholder="Enter postal code..."
                              className="cart-page-input-text"
                              value={shippingInfo.postalCode}
                              onChange={(e) => handleShippingInfoChange('postalCode', e.target.value)}
                            />
                          </div>

                          {/* Shipping Method */}
                          <div className="form-group mb-3">
                            <label className="form-label">Shipping Method *</label>
                            <div className="outline-select">
                              <select
                                value={shippingInfo.shippingMethod}
                                onChange={(e) => handleShippingInfoChange('shippingMethod', e.target.value)}
                              >
                                {getShippingMethods(shippingInfo.country).map(method => (
                                  <option key={method.value} value={method.value}>
                                    {method.name} ({method.days} days) - ${method.base.toFixed(2)}+
                                  </option>
                                ))}
                              </select>
                              <span className="select-icon">
                                <i className="icofont-rounded-down"></i>
                              </span>
                            </div>
                            <small className="text-muted mt-1 d-block">
                              {getShippingMethods(shippingInfo.country).find(m => m.value === shippingInfo.shippingMethod)?.description}
                            </small>
                          </div>

                          {/* Address */}
                          <div className="form-group mb-3">
                            <label className="form-label">Street Address</label>
                            <textarea
                              className="cart-page-input-text"
                              placeholder="Enter your full address..."
                              rows="3"
                              value={shippingInfo.address}
                              onChange={(e) => handleShippingInfoChange('address', e.target.value)}
                            ></textarea>
                          </div>

                          {/* Package Information */}
                          <div className="shipping-info mb-3">
                            <h6>Package Information</h6>
                            <div className="info-row">
                              <span>Total Items:</span>
                              <span>{cartItems.reduce((total, item) => total + item.quantity, 0)} books</span>
                            </div>
                            <div className="info-row">
                              <span>Estimated Weight:</span>
                              <span>{calculateWeight().toFixed(1)} kg</span>
                            </div>
                          </div>

                          <button
                            type="button"
                            className="btn btn-primary w-100"
                            onClick={calculateShipping}
                          >
                            Calculate Shipping Cost
                          </button>

                          {/* Shipping Results */}
                          {shippingCalculated && (
                            <div className="shipping-results mt-3 p-3 border rounded">
                              <h6>Shipping Details</h6>
                              <div className="info-row">
                                <span>Shipping Cost:</span>
                                <span className="fw-bold">
                                  {shippingCost === 0 ? 'FREE' : `$${shippingCost.toFixed(2)}`}
                                </span>
                              </div>
                              <div className="info-row">
                                <span>Estimated Delivery:</span>
                                <span>{estimatedDelivery} business days</span>
                              </div>
                              {cartSubTotal >= 50 && shippingCost === 0 && (
                                <div className="free-shipping-notice text-success mt-2">
                                  <i className="icofont-check-circled me-1"></i>
                                  Free shipping on orders over $50!
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="col-md-6 col-12">
                        <div className="cart-overview">
                          <h3>Cart Totals</h3>
                          <ul className="lab-ul">
                            <li>
                              <span className="pull-left">Cart Subtotal</span>
                              <p className="pull-right">
                                $ {cartSubTotal.toFixed(2)}
                              </p>
                            </li>
                            <li>
                              <span className="pull-left">Items Count</span>
                              <p className="pull-right">
                                {cartItems.reduce((total, item) => total + item.quantity, 0)} books
                              </p>
                            </li>
                            <li>
                              <span className="pull-left">Package Weight</span>
                              <p className="pull-right">
                                {calculateWeight().toFixed(1)} kg
                              </p>
                            </li>
                            <li>
                              <span className="pull-left">
                                Shipping and Handling
                              </span>
                              <p className="pull-right">
                                {!shippingCalculated ? (
                                  <span className="text-muted">Calculate shipping</span>
                                ) : shippingCost === 0 ? (
                                  <span className="text-success">FREE</span>
                                ) : (
                                  `$${shippingCost.toFixed(2)}`
                                )}
                              </p>
                            </li>
                            {shippingCalculated && estimatedDelivery && (
                              <li>
                                <span className="pull-left">Estimated Delivery</span>
                                <p className="pull-right">
                                  {estimatedDelivery} days
                                </p>
                              </li>
                            )}
                            {cartSubTotal >= 50 && (
                              <li className="free-shipping-threshold">
                                <span className="pull-left text-success">
                                  <i className="icofont-check-circled me-1"></i>
                                  Free Shipping Eligible
                                </span>
                                <p className="pull-right text-success">
                                  ✓
                                </p>
                              </li>
                            )}
                            {cartSubTotal < 50 && (
                              <li className="free-shipping-threshold">
                                <span className="pull-left text-info">
                                  <i className="icofont-info-circle me-1"></i>
                                  Add ${(50 - cartSubTotal).toFixed(2)} for free shipping
                                </span>
                                <p className="pull-right">

                                </p>
                              </li>
                            )}
                            <li className="order-total-row">
                              <span className="pull-left fw-bold">Order Total</span>
                              <p className="pull-right fw-bold fs-5">
                                $ {orderTotal.toFixed(2)}
                              </p>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPage;

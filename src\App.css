.nav-profile {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: indigo;
  cursor: pointer;
}

.page-item {
  width: 46px;
  height: 46px;
  text-align: center;
  border-radius: 50%;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.06);
  background: #fff;
  color: #101115;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 7px;
}

.github {
  height: 36px;
  width: 36px;
  line-height: 36px;
  transition: all 0.3s ease;
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background: #0f9d58;
  color: #fff;
}

/* Breadcrumb styles */
.breadcrumb-item {
  color: #fff !important;
}

.breadcrumb-item + .breadcrumb-item::before {
  color: #fff !important;
  content: "/" !important;
}

.breadcrumb-item.active {
  color: #fff !important;
}

/* Page header styles */
.pageheader-section {
  transition: background-image 0.3s ease-in-out;
}

.pageheader-content h2 {
  font-size: 2.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Enhance navigation visibility */
.nav-link {
  color: #fff !important;
  transition: all 0.3s ease;
}

.nav-link:hover {
  opacity: 0.8;
}

/* Mobile/Tablet Navigation Styles with Bootstrap */
@media screen and (max-width: 992px) {
  .navbar-collapse.show,
  .navbar-collapse.collapsing,
  .menu .lab-ul.active {
    background-color: #fff !important;
    padding: 1rem 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .menu .lab-ul.active li a,
  .navbar-nav .nav-link,
  .navbar-nav .nav-item a {
    color: #000 !important;
    padding: 0.75rem 1.25rem !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  .menu .lab-ul.active li:last-child a,
  .navbar-nav .nav-item:last-child a {
    border-bottom: none;
  }

  .navbar-toggler-icon,
  .header-bar span {
    background-color: #000 !important;
  }

  .header-bottom {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
}

/* Custom styles for quantity control buttons */
.qty-btn {
  width: 40px !important;
  height: 40px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.25rem !important;
}

.qty-btn i {
  font-size: inherit;
  line-height: 1;
}

/* Cart Icon Styles */
.cart-icon-wrapper {
  margin-left: 20px;
  display: flex;
  align-items: center;
}

.cart-icon-link {
  text-decoration: none !important;
  color: inherit;
  transition: all 0.3s ease;
}

.cart-icon-link:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

.cart-icon {
  position: relative;
  padding: 8px;
}

.cart-badge {
  font-size: 0.7rem !important;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  padding: 0 4px;
}

/* Responsive cart icon */
@media screen and (max-width: 992px) {
  .cart-icon-wrapper {
    margin-left: 15px;
  }

  .menu .lab-ul.active ~ .cart-icon-wrapper .cart-icon-link {
    color: #000 !important;
  }
}

/* Enhanced Shipping Calculation Styles */
.calculate-shiping .form-group {
  margin-bottom: 1rem;
}

.calculate-shiping .form-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  display: block;
  font-size: 0.9rem;
}

.shipping-info {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.shipping-info h6 {
  color: #495057;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.info-row:last-child {
  margin-bottom: 0;
}

.shipping-results {
  background-color: #e8f5e8;
  border-color: #28a745 !important;
}

.shipping-results h6 {
  color: #155724;
  margin-bottom: 0.75rem;
}

.free-shipping-notice {
  font-size: 0.85rem;
  font-weight: 500;
}

.cart-overview .free-shipping-threshold {
  background-color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 4px;
  margin: 0.5rem 0;
}

.cart-overview .order-total-row {
  border-top: 2px solid #dee2e6;
  padding-top: 0.75rem;
  margin-top: 0.75rem;
}

.cart-overview .order-total-row span,
.cart-overview .order-total-row p {
  font-size: 1.1rem;
}

/* Outline select improvements */
.outline-select {
  position: relative;
  margin-bottom: 0;
}

.outline-select select {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  background-color: #fff;
  font-size: 0.9rem;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.outline-select select:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.outline-select .select-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: #6c757d;
}

/* Cart page input text improvements */
.cart-page-input-text {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  font-size: 0.9rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.cart-page-input-text:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.cart-page-input-text::placeholder {
  color: #6c757d;
  opacity: 1;
}

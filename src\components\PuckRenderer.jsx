/*
import React from "react";
import { Render } from "@measured/puck";
import config from "./puck-config"; // Create this file with the same components as your editor

// Simple version - use Puck's built-in Render component
const PuckRenderer = ({ data }) => {
  if (!data) return null;

  console.log("Rendering Puck data:", data);

  return <Render config={config} data={data} />;
};

export default PuckRenderer;
*/
